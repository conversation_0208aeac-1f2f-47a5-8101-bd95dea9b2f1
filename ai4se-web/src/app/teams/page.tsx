import MainLayout from '@/components/layout/MainLayout';
import { <PERSON>, CardHeader, CardContent, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';

export default function TeamsPage() {
  const teams = [
    {
      id: 1,
      name: 'Frontend Development',
      description: 'Building amazing user interfaces with React and TypeScript',
      members: 8,
      projects: 12,
      status: 'active',
      avatar: 'FD'
    },
    {
      id: 2,
      name: 'Backend Engineering',
      description: 'Scalable APIs and microservices architecture',
      members: 6,
      projects: 8,
      status: 'active',
      avatar: 'BE'
    },
    {
      id: 3,
      name: 'DevOps & Infrastructure',
      description: 'Cloud infrastructure and deployment automation',
      members: 4,
      projects: 15,
      status: 'active',
      avatar: 'DI'
    },
    {
      id: 4,
      name: 'AI Research',
      description: 'Machine learning and artificial intelligence research',
      members: 5,
      projects: 6,
      status: 'planning',
      avatar: 'AI'
    }
  ];

  const teamMembers = [
    { name: '<PERSON>', role: 'Team Lead', avatar: '<PERSON>', status: 'online' },
    { name: '<PERSON>', role: 'Senior Developer', avatar: 'BS', status: 'online' },
    { name: '<PERSON>', role: 'UI/UX Designer', avatar: 'CD', status: 'away' },
    { name: '<PERSON>', role: 'Backend Developer', avatar: 'DW', status: 'offline' },
    { name: 'Eva Brown', role: 'QA Engineer', avatar: 'EB', status: 'online' },
  ];

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="kt-container-fixed">
          <div className="flex flex-wrap items-center lg:items-end justify-between gap-5 pb-7.5">
            <div className="flex flex-col justify-center gap-2">
              <h1 className="text-xl font-medium leading-none text-mono">
                Teams
              </h1>
              <div className="flex items-center gap-2 text-sm font-normal text-secondary-foreground">
                Collaborate with your team members and manage projects
              </div>
            </div>
            <div className="flex items-center gap-2.5">
              <Button variant="outline">
                Invite Members
              </Button>
              <Button>
                Create Team
              </Button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="kt-container-fixed">
          <div className="grid gap-5 lg:gap-7.5">
            {/* Teams Grid */}
            <div className="grid lg:grid-cols-2 gap-5 lg:gap-7.5">
              {teams.map((team) => (
                <Card key={team.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-12 h-12 bg-primary rounded-lg flex items-center justify-center text-white font-bold">
                          {team.avatar}
                        </div>
                        <div>
                          <CardTitle className="text-lg">{team.name}</CardTitle>
                          <Badge 
                            variant={team.status === 'active' ? 'success' : 'warning'} 
                            size="sm"
                          >
                            {team.status}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground mb-4">{team.description}</p>
                    <div className="flex justify-between items-center mb-4">
                      <div className="flex gap-4">
                        <div className="text-center">
                          <div className="text-lg font-semibold text-foreground">{team.members}</div>
                          <div className="text-xs text-muted-foreground">Members</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-semibold text-foreground">{team.projects}</div>
                          <div className="text-xs text-muted-foreground">Projects</div>
                        </div>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" className="flex-1">
                        View Details
                      </Button>
                      <Button size="sm" className="flex-1">
                        Join Team
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Team Members and Activity */}
            <div className="grid lg:grid-cols-3 gap-5 lg:gap-7.5">
              {/* Team Members */}
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle>Team Members</CardTitle>
                      <div className="flex items-center gap-2">
                        <input
                          type="text"
                          placeholder="Search members..."
                          className="px-3 py-1.5 text-sm border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        />
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {teamMembers.map((member, index) => (
                        <div key={index} className="flex items-center justify-between p-3 border border-border rounded-lg hover:bg-accent transition-colors">
                          <div className="flex items-center gap-3">
                            <div className="relative">
                              <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-medium">
                                {member.avatar}
                              </div>
                              <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-background ${
                                member.status === 'online' ? 'bg-green-500' : 
                                member.status === 'away' ? 'bg-yellow-500' : 'bg-gray-400'
                              }`}></div>
                            </div>
                            <div>
                              <div className="font-medium text-foreground">{member.name}</div>
                              <div className="text-sm text-muted-foreground">{member.role}</div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge 
                              variant={member.status === 'online' ? 'success' : member.status === 'away' ? 'warning' : 'default'} 
                              size="sm"
                            >
                              {member.status}
                            </Badge>
                            <Button variant="ghost" size="sm">
                              Message
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Team Activity */}
              <div className="lg:col-span-1">
                <Card className="h-full">
                  <CardHeader>
                    <CardTitle>Recent Activity</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                        <div>
                          <p className="text-sm text-foreground">Alice completed project review</p>
                          <p className="text-xs text-muted-foreground">2 hours ago</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                        <div>
                          <p className="text-sm text-foreground">Bob pushed new code</p>
                          <p className="text-xs text-muted-foreground">4 hours ago</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
                        <div>
                          <p className="text-sm text-foreground">Carol updated designs</p>
                          <p className="text-xs text-muted-foreground">6 hours ago</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
                        <div>
                          <p className="text-sm text-foreground">David fixed critical bug</p>
                          <p className="text-xs text-muted-foreground">1 day ago</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                        <div>
                          <p className="text-sm text-foreground">Eva reported test results</p>
                          <p className="text-xs text-muted-foreground">2 days ago</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
