import MainLayout from '@/components/layout/MainLayout';
import { Card, CardHeader, CardContent, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';

export default function ProfilePage() {
  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="kt-container-fixed">
          <div className="flex flex-wrap items-center lg:items-end justify-between gap-5 pb-7.5">
            <div className="flex flex-col justify-center gap-2">
              <h1 className="text-xl font-medium leading-none text-mono">
                Profile
              </h1>
              <div className="flex items-center gap-2 text-sm font-normal text-secondary-foreground">
                Manage your personal information and preferences
              </div>
            </div>
            <div className="flex items-center gap-2.5">
              <Button variant="outline">
                Edit Profile
              </Button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="kt-container-fixed">
          <div className="grid gap-5 lg:gap-7.5">
            {/* Profile Info Grid */}
            <div className="grid lg:grid-cols-3 gap-5 lg:gap-7.5 items-stretch">
              {/* Profile Card */}
              <div className="lg:col-span-1">
                <Card className="h-full">
                  <CardContent className="flex flex-col items-center text-center p-8">
                    <div className="w-24 h-24 bg-primary rounded-full flex items-center justify-center text-white text-2xl font-bold mb-4">
                      JD
                    </div>
                    <h2 className="text-xl font-semibold text-foreground mb-2">John Doe</h2>
                    <p className="text-muted-foreground mb-4">Senior Software Engineer</p>
                    <div className="flex flex-wrap gap-2 mb-6">
                      <Badge variant="success">Active</Badge>
                      <Badge variant="info">Premium</Badge>
                    </div>
                    <div className="w-full space-y-3">
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Projects</span>
                        <span className="font-medium">24</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Team Members</span>
                        <span className="font-medium">12</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Completed Tasks</span>
                        <span className="font-medium">156</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Personal Information */}
              <div className="lg:col-span-2">
                <Card className="h-full">
                  <CardHeader>
                    <CardTitle>Personal Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-foreground mb-2">
                          First Name
                        </label>
                        <input
                          type="text"
                          value="John"
                          className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                          readOnly
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-foreground mb-2">
                          Last Name
                        </label>
                        <input
                          type="text"
                          value="Doe"
                          className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                          readOnly
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-foreground mb-2">
                          Email
                        </label>
                        <input
                          type="email"
                          value="<EMAIL>"
                          className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                          readOnly
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-foreground mb-2">
                          Phone
                        </label>
                        <input
                          type="tel"
                          value="+****************"
                          className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                          readOnly
                        />
                      </div>
                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-foreground mb-2">
                          Bio
                        </label>
                        <textarea
                          value="Passionate software engineer with 8+ years of experience in full-stack development. Specialized in React, Node.js, and cloud technologies."
                          className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent h-24 resize-none"
                          readOnly
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Skills and Activity */}
            <div className="grid lg:grid-cols-2 gap-5 lg:gap-7.5 items-stretch">
              {/* Skills */}
              <Card>
                <CardHeader>
                  <CardTitle>Skills & Technologies</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="info">React</Badge>
                    <Badge variant="info">TypeScript</Badge>
                    <Badge variant="info">Node.js</Badge>
                    <Badge variant="info">Python</Badge>
                    <Badge variant="info">AWS</Badge>
                    <Badge variant="info">Docker</Badge>
                    <Badge variant="info">Kubernetes</Badge>
                    <Badge variant="info">GraphQL</Badge>
                    <Badge variant="info">PostgreSQL</Badge>
                    <Badge variant="info">Redis</Badge>
                  </div>
                </CardContent>
              </Card>

              {/* Recent Activity */}
              <Card>
                <CardHeader>
                  <CardTitle>Recent Activity</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                      <div>
                        <p className="text-sm text-foreground">Completed project "AI Dashboard"</p>
                        <p className="text-xs text-muted-foreground">2 hours ago</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                      <div>
                        <p className="text-sm text-foreground">Updated profile information</p>
                        <p className="text-xs text-muted-foreground">1 day ago</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
                      <div>
                        <p className="text-sm text-foreground">Joined team "Frontend Development"</p>
                        <p className="text-xs text-muted-foreground">3 days ago</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
                      <div>
                        <p className="text-sm text-foreground">Created new repository</p>
                        <p className="text-xs text-muted-foreground">1 week ago</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
