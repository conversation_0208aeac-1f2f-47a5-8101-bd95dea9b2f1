@import "tailwindcss";

/* Demo1 Template CSS Variables */
:root {
  /* Colors */
  --background: #ffffff;
  --foreground: #1e1e2e;
  --muted: #f8f9fa;
  --muted-foreground: #6c757d;
  --secondary-foreground: #6c757d;
  --accent: #f8f9fa;
  --border: #e9ecef;
  --primary: #3b82f6;
  --mono: #1e1e2e;

  /* Spacing */
  --kt-container-fixed-max-width: 1320px;
  --kt-sidebar-width: 280px;
  --kt-header-height: 70px;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-accent: var(--accent);
  --color-border: var(--border);
  --color-primary: var(--primary);
  --color-mono: var(--mono);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #1a1a1a;
    --foreground: #ffffff;
    --muted: #2a2a2a;
    --muted-foreground: #a1a1aa;
    --secondary-foreground: #a1a1aa;
    --accent: #2a2a2a;
    --border: #3a3a3a;
    --primary: #3b82f6;
    --mono: #ffffff;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
}

/* Demo1 Template Base Styles */
.kt-container-fixed {
  max-width: var(--kt-container-fixed-max-width);
  margin: 0 auto;
  padding: 0 1rem;
}

.kt-sidebar {
  width: var(--kt-sidebar-width);
  background: var(--background);
  border-right: 1px solid var(--border);
}

.kt-header {
  height: var(--kt-header-height);
  background: var(--background);
  border-bottom: 1px solid var(--border);
}

.kt-card {
  background: var(--background);
  border: 1px solid var(--border);
  border-radius: 0.75rem;
  padding: 1.5rem;
}

.kt-card-header {
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border);
  margin-bottom: 1rem;
}

.kt-card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--foreground);
}

.kt-card-content {
  padding: 0;
}

.kt-card-footer {
  padding-top: 1rem;
  border-top: 1px solid var(--border);
  margin-top: 1rem;
}

.kt-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s;
  cursor: pointer;
  border: none;
  text-decoration: none;
}

.kt-btn-primary {
  background: var(--primary);
  color: white;
}

.kt-btn-primary:hover {
  background: #2563eb;
}

.kt-btn-outline {
  background: transparent;
  color: var(--foreground);
  border: 1px solid var(--border);
}

.kt-btn-outline:hover {
  background: var(--accent);
}

.kt-btn-ghost {
  background: transparent;
  color: var(--foreground);
}

.kt-btn-ghost:hover {
  background: var(--accent);
}

.kt-btn-icon {
  padding: 0.5rem;
  width: 2.5rem;
  height: 2.5rem;
}

.kt-menu {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.kt-menu-item {
  position: relative;
}

.kt-menu-link {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  color: var(--foreground);
  text-decoration: none;
  transition: all 0.2s;
  gap: 0.75rem;
}

.kt-menu-link:hover {
  background: var(--accent);
  color: var(--primary);
}

.kt-menu-link.active {
  background: var(--primary);
  color: white;
}

.kt-menu-icon {
  width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.kt-menu-title {
  font-size: 0.875rem;
  font-weight: 500;
}
