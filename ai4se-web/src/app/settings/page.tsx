import MainLayout from '@/components/layout/MainLayout';
import { Card, CardHeader, CardContent, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';

export default function SettingsPage() {
  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="kt-container-fixed">
          <div className="flex flex-wrap items-center lg:items-end justify-between gap-5 pb-7.5">
            <div className="flex flex-col justify-center gap-2">
              <h1 className="text-xl font-medium leading-none text-mono">
                Settings
              </h1>
              <div className="flex items-center gap-2 text-sm font-normal text-secondary-foreground">
                Manage your account settings and preferences
              </div>
            </div>
            <div className="flex items-center gap-2.5">
              <Button>
                Save Changes
              </Button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="kt-container-fixed">
          <div className="grid gap-5 lg:gap-7.5">
            {/* Settings Grid */}
            <div className="grid lg:grid-cols-3 gap-5 lg:gap-7.5">
              {/* Settings Navigation */}
              <div className="lg:col-span-1">
                <Card>
                  <CardHeader>
                    <CardTitle>Settings</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <nav className="space-y-2">
                      <a href="#general" className="block px-3 py-2 text-sm font-medium text-primary bg-accent rounded-lg">
                        General
                      </a>
                      <a href="#security" className="block px-3 py-2 text-sm font-medium text-foreground hover:bg-accent rounded-lg transition-colors">
                        Security
                      </a>
                      <a href="#notifications" className="block px-3 py-2 text-sm font-medium text-foreground hover:bg-accent rounded-lg transition-colors">
                        Notifications
                      </a>
                      <a href="#privacy" className="block px-3 py-2 text-sm font-medium text-foreground hover:bg-accent rounded-lg transition-colors">
                        Privacy
                      </a>
                      <a href="#integrations" className="block px-3 py-2 text-sm font-medium text-foreground hover:bg-accent rounded-lg transition-colors">
                        Integrations
                      </a>
                    </nav>
                  </CardContent>
                </Card>
              </div>

              {/* Settings Content */}
              <div className="lg:col-span-2 space-y-6">
                {/* General Settings */}
                <Card id="general">
                  <CardHeader>
                    <CardTitle>General Settings</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Language
                      </label>
                      <select className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="en">English</option>
                        <option value="es">Spanish</option>
                        <option value="fr">French</option>
                        <option value="de">German</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Timezone
                      </label>
                      <select className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="UTC">UTC</option>
                        <option value="EST">Eastern Time</option>
                        <option value="PST">Pacific Time</option>
                        <option value="GMT">Greenwich Mean Time</option>
                      </select>
                    </div>
                    <div>
                      <label className="flex items-center gap-3">
                        <input type="checkbox" className="w-4 h-4 text-primary border-border rounded focus:ring-primary" defaultChecked />
                        <span className="text-sm font-medium text-foreground">Enable dark mode</span>
                      </label>
                    </div>
                    <div>
                      <label className="flex items-center gap-3">
                        <input type="checkbox" className="w-4 h-4 text-primary border-border rounded focus:ring-primary" />
                        <span className="text-sm font-medium text-foreground">Show advanced features</span>
                      </label>
                    </div>
                  </CardContent>
                </Card>

                {/* Security Settings */}
                <Card id="security">
                  <CardHeader>
                    <CardTitle>Security Settings</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Current Password
                      </label>
                      <input
                        type="password"
                        className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        placeholder="Enter current password"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        New Password
                      </label>
                      <input
                        type="password"
                        className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        placeholder="Enter new password"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Confirm New Password
                      </label>
                      <input
                        type="password"
                        className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        placeholder="Confirm new password"
                      />
                    </div>
                    <div>
                      <label className="flex items-center gap-3">
                        <input type="checkbox" className="w-4 h-4 text-primary border-border rounded focus:ring-primary" defaultChecked />
                        <span className="text-sm font-medium text-foreground">Enable two-factor authentication</span>
                      </label>
                    </div>
                    <div className="pt-4">
                      <Button variant="outline">
                        Update Password
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Notification Settings */}
                <Card id="notifications">
                  <CardHeader>
                    <CardTitle>Notification Settings</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <label className="flex items-center gap-3">
                        <input type="checkbox" className="w-4 h-4 text-primary border-border rounded focus:ring-primary" defaultChecked />
                        <span className="text-sm font-medium text-foreground">Email notifications</span>
                      </label>
                    </div>
                    <div>
                      <label className="flex items-center gap-3">
                        <input type="checkbox" className="w-4 h-4 text-primary border-border rounded focus:ring-primary" defaultChecked />
                        <span className="text-sm font-medium text-foreground">Push notifications</span>
                      </label>
                    </div>
                    <div>
                      <label className="flex items-center gap-3">
                        <input type="checkbox" className="w-4 h-4 text-primary border-border rounded focus:ring-primary" />
                        <span className="text-sm font-medium text-foreground">SMS notifications</span>
                      </label>
                    </div>
                    <div>
                      <label className="flex items-center gap-3">
                        <input type="checkbox" className="w-4 h-4 text-primary border-border rounded focus:ring-primary" defaultChecked />
                        <span className="text-sm font-medium text-foreground">Weekly digest</span>
                      </label>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
