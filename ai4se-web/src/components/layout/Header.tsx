'use client';

import React, { useState } from 'react';
import Link from 'next/link';

interface HeaderProps {
  onToggleSidebar: () => void;
}

const Header: React.FC<HeaderProps> = ({ onToggleSidebar }) => {
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);

  const toggleUserMenu = () => {
    setShowUserMenu(!showUserMenu);
    setShowNotifications(false);
  };

  const toggleNotifications = () => {
    setShowNotifications(!showNotifications);
    setShowUserMenu(false);
  };

  return (
    <header className="kt-header fixed top-0 z-10 start-0 end-0 flex items-stretch shrink-0 bg-background border-b border-border">
      {/* Container */}
      <div className="kt-container-fixed flex justify-between items-stretch lg:gap-4">
        {/* Mobile Logo & Menu Toggle */}
        <div className="flex gap-2.5 lg:hidden items-center -ms-1">
          <Link href="/" className="shrink-0">
            <span className="text-lg font-bold text-primary">AI4SE</span>
          </Link>
          <div className="flex items-center">
            <button 
              className="kt-btn kt-btn-icon kt-btn-ghost"
              onClick={onToggleSidebar}
            >
              ☰
            </button>
          </div>
        </div>

        {/* Navigation Menu */}
        <div className="hidden lg:flex items-stretch">
          <div className="flex items-stretch">
            <div className="flex items-stretch">
              <div className="flex flex-row gap-5 lg:gap-7.5">
                <div className="flex items-center">
                  <Link 
                    href="/"
                    className="text-sm text-foreground font-medium hover:text-primary transition-colors"
                  >
                    Home
                  </Link>
                </div>
                <div className="flex items-center">
                  <Link 
                    href="/profile"
                    className="text-sm text-secondary-foreground font-medium hover:text-primary transition-colors"
                  >
                    Profile
                  </Link>
                </div>
                <div className="flex items-center">
                  <Link 
                    href="/teams"
                    className="text-sm text-secondary-foreground font-medium hover:text-primary transition-colors"
                  >
                    Teams
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Side - Search, Notifications, User */}
        <div className="flex items-center gap-2 lg:gap-3.5">
          {/* Search */}
          <div className="hidden lg:flex">
            <div className="relative">
              <input
                type="text"
                placeholder="Search..."
                className="w-64 px-4 py-2 pl-10 text-sm border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span className="text-muted-foreground">🔍</span>
              </div>
            </div>
          </div>

          {/* Notifications */}
          <div className="relative">
            <button
              className="kt-btn kt-btn-icon kt-btn-ghost relative"
              onClick={toggleNotifications}
            >
              🔔
              <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
            </button>
            
            {showNotifications && (
              <div className="absolute right-0 mt-2 w-80 bg-background border border-border rounded-lg shadow-lg z-50">
                <div className="p-4 border-b border-border">
                  <h3 className="text-sm font-semibold text-foreground">Notifications</h3>
                </div>
                <div className="p-4">
                  <div className="space-y-3">
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
                      <div>
                        <p className="text-sm text-foreground">New team member joined</p>
                        <p className="text-xs text-muted-foreground">2 minutes ago</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                      <div>
                        <p className="text-sm text-foreground">Project completed successfully</p>
                        <p className="text-xs text-muted-foreground">1 hour ago</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
                      <div>
                        <p className="text-sm text-foreground">System maintenance scheduled</p>
                        <p className="text-xs text-muted-foreground">3 hours ago</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="p-4 border-t border-border">
                  <Link 
                    href="/notifications"
                    className="text-sm text-primary hover:underline"
                  >
                    View all notifications
                  </Link>
                </div>
              </div>
            )}
          </div>

          {/* User Menu */}
          <div className="relative">
            <button
              className="flex items-center gap-2 p-2 rounded-lg hover:bg-accent transition-colors"
              onClick={toggleUserMenu}
            >
              <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white text-sm font-medium">
                U
              </div>
              <span className="hidden lg:block text-sm font-medium text-foreground">User</span>
              <span className="text-muted-foreground">▼</span>
            </button>

            {showUserMenu && (
              <div className="absolute right-0 mt-2 w-48 bg-background border border-border rounded-lg shadow-lg z-50">
                <div className="p-4 border-b border-border">
                  <p className="text-sm font-medium text-foreground">John Doe</p>
                  <p className="text-xs text-muted-foreground"><EMAIL></p>
                </div>
                <div className="py-2">
                  <Link 
                    href="/profile"
                    className="block px-4 py-2 text-sm text-foreground hover:bg-accent transition-colors"
                  >
                    Profile
                  </Link>
                  <Link 
                    href="/settings"
                    className="block px-4 py-2 text-sm text-foreground hover:bg-accent transition-colors"
                  >
                    Settings
                  </Link>
                  <Link 
                    href="/help"
                    className="block px-4 py-2 text-sm text-foreground hover:bg-accent transition-colors"
                  >
                    Help
                  </Link>
                </div>
                <div className="py-2 border-t border-border">
                  <button type="button" className="block w-full text-left px-4 py-2 text-sm text-foreground hover:bg-accent transition-colors">
                    Sign out
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
