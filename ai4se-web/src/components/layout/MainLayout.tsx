'use client';

import React, { useState } from 'react';
import Sidebar from './Sidebar';
import Header from './Header';

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  return (
    <div className="antialiased flex h-full text-base text-foreground bg-background">
      {/* Sidebar */}
      <Sidebar collapsed={sidebarCollapsed} onToggle={toggleSidebar} />
      
      {/* Main Content */}
      <div className="flex grow">
        {/* Header */}
        <div className="flex flex-col w-full">
          <Header onToggleSidebar={toggleSidebar} />
          
          {/* Content */}
          <main className="grow pt-5" id="content" role="content">
            {children}
          </main>
        </div>
      </div>
    </div>
  );
};

export default MainLayout;
