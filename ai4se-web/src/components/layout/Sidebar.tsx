'use client';

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { usePathname } from 'next/navigation';

interface SidebarProps {
  collapsed: boolean;
  onToggle: () => void;
}

interface MenuItem {
  id: string;
  title: string;
  icon: string;
  href?: string;
  children?: MenuItem[];
}

const menuItems: MenuItem[] = [
  {
    id: 'dashboards',
    title: 'Dashboards',
    icon: '📊',
    children: [
      { id: 'dashboard-main', title: 'Main Dashboard', icon: '🏠', href: '/' },
      { id: 'dashboard-analytics', title: 'Analytics', icon: '📈', href: '/analytics' },
    ]
  },
  {
    id: 'user',
    title: 'User',
    icon: '👤',
    children: [
      { id: 'profile', title: 'Profile', icon: '👤', href: '/profile' },
      { id: 'settings', title: 'Settings', icon: '⚙️', href: '/settings' },
    ]
  },
  {
    id: 'teams',
    title: 'Teams',
    icon: '👥',
    href: '/teams'
  },
  {
    id: 'network',
    title: 'Network',
    icon: '🌐',
    children: [
      { id: 'user-cards', title: 'User Cards', icon: '🃏', href: '/network/user-cards' },
      { id: 'user-table', title: 'User Table', icon: '📋', href: '/network/user-table' },
    ]
  }
];

const Sidebar: React.FC<SidebarProps> = ({ collapsed, onToggle }) => {
  const pathname = usePathname();
  const [expandedItems, setExpandedItems] = useState<string[]>(['dashboards']);

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const isActive = (href: string) => pathname === href;

  const renderMenuItem = (item: MenuItem, level = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.includes(item.id);
    const isItemActive = item.href ? isActive(item.href) : false;

    return (
      <div key={item.id} className="kt-menu-item">
        {hasChildren ? (
          <>
            <div 
              className="kt-menu-link flex items-center grow cursor-pointer border border-transparent gap-[10px] ps-[10px] pe-[10px] py-[6px]"
              onClick={() => toggleExpanded(item.id)}
            >
              <span className="kt-menu-icon items-start text-muted-foreground w-[20px]">
                {item.icon}
              </span>
              <span className="kt-menu-title text-sm font-medium text-foreground">
                {item.title}
              </span>
              <span className="kt-menu-arrow text-muted-foreground w-[20px] shrink-0 justify-end ms-1 me-[-10px]">
                {isExpanded ? '−' : '+'}
              </span>
            </div>
            {isExpanded && (
              <div className="kt-menu-accordion gap-1 ps-[10px] relative before:absolute before:start-[20px] before:top-0 before:bottom-0 before:border-s before:border-border">
                {item.children?.map(child => renderMenuItem(child, level + 1))}
              </div>
            )}
          </>
        ) : (
          <Link 
            href={item.href || '#'}
            className={`kt-menu-link border border-transparent items-center grow hover:bg-accent/60 hover:rounded-lg gap-[14px] ps-[10px] pe-[10px] py-[8px] ${
              isItemActive ? 'bg-accent/60 rounded-lg text-primary font-semibold' : ''
            }`}
          >
            <span className="kt-menu-bullet flex w-[6px] -start-[3px] relative before:absolute before:top-0 before:size-[6px] before:rounded-full before:-translate-y-1/2">
            </span>
            <span className="kt-menu-title text-2sm font-normal text-foreground">
              {item.title}
            </span>
          </Link>
        )}
      </div>
    );
  };

  return (
    <div className={`kt-sidebar bg-background border-e border-e-border fixed top-0 bottom-0 z-20 lg:flex flex-col items-stretch shrink-0 ${collapsed ? 'hidden' : 'flex'}`}>
      {/* Sidebar Header */}
      <div className="kt-sidebar-header flex items-center relative justify-between px-3 lg:px-6 shrink-0 h-[70px]">
        <Link href="/" className="flex items-center">
          <span className="text-xl font-bold text-primary">AI4SE</span>
        </Link>
        <button
          type="button"
          className="kt-btn kt-btn-outline kt-btn-icon size-[30px] lg:hidden"
          onClick={onToggle}
        >
          ×
        </button>
      </div>

      {/* Sidebar Content */}
      <div className="kt-sidebar-content flex grow shrink-0 py-5 pe-2">
        <div className="grow shrink-0 flex ps-2 lg:ps-5 pe-1 lg:pe-3 overflow-y-auto">
          {/* Sidebar Menu */}
          <div className="kt-menu flex flex-col grow gap-1">
            {menuItems.map(item => renderMenuItem(item))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
