'use client';

import React from 'react';
import { Card, Card<PERSON>eader, CardContent, CardTitle } from '../ui/Card';
import Button from '../ui/Button';
import Badge from '../ui/Badge';

const Dashboard: React.FC = () => {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="kt-container-fixed">
        <div className="flex flex-wrap items-center lg:items-end justify-between gap-5 pb-7.5">
          <div className="flex flex-col justify-center gap-2">
            <h1 className="text-xl font-medium leading-none text-mono">
              Dashboard
            </h1>
            <div className="flex items-center gap-2 text-sm font-normal text-secondary-foreground">
              Central Hub for Personal Customization
            </div>
          </div>
          <div className="flex items-center gap-2.5">
            <Button variant="outline">
              View Profile
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="kt-container-fixed">
        <div className="grid gap-5 lg:gap-7.5">
          {/* Stats Grid */}
          <div className="grid lg:grid-cols-3 gap-y-5 lg:gap-7.5 items-stretch">
            {/* Stats Cards */}
            <div className="lg:col-span-1">
              <div className="grid grid-cols-2 gap-5 lg:gap-7.5 h-full items-stretch">
                <Card className="flex-col justify-between gap-6 h-full bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
                  <div className="flex items-center justify-between">
                    <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center text-white">
                      👥
                    </div>
                    <Badge variant="success" size="sm">+2.7%</Badge>
                  </div>
                  <div className="flex flex-col gap-1">
                    <span className="text-3xl font-semibold text-mono">
                      9.3k
                    </span>
                    <span className="text-sm font-normal text-secondary-foreground">
                      Team Members
                    </span>
                  </div>
                </Card>

                <Card className="flex-col justify-between gap-6 h-full bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
                  <div className="flex items-center justify-between">
                    <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center text-white">
                      📊
                    </div>
                    <Badge variant="success" size="sm">+5.2%</Badge>
                  </div>
                  <div className="flex flex-col gap-1">
                    <span className="text-3xl font-semibold text-mono">
                      24k
                    </span>
                    <span className="text-sm font-normal text-secondary-foreground">
                      Projects
                    </span>
                  </div>
                </Card>

                <Card className="flex-col justify-between gap-6 h-full bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
                  <div className="flex items-center justify-between">
                    <div className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center text-white">
                      🎯
                    </div>
                    <Badge variant="warning" size="sm">-1.3%</Badge>
                  </div>
                  <div className="flex flex-col gap-1">
                    <span className="text-3xl font-semibold text-mono">
                      608
                    </span>
                    <span className="text-sm font-normal text-secondary-foreground">
                      Active Tasks
                    </span>
                  </div>
                </Card>

                <Card className="flex-col justify-between gap-6 h-full bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20">
                  <div className="flex items-center justify-between">
                    <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center text-white">
                      ⚡
                    </div>
                    <Badge variant="info" size="sm">+8.1%</Badge>
                  </div>
                  <div className="flex flex-col gap-1">
                    <span className="text-3xl font-semibold text-mono">
                      2.5k
                    </span>
                    <span className="text-sm font-normal text-secondary-foreground">
                      Completed
                    </span>
                  </div>
                </Card>
              </div>
            </div>

            {/* Welcome Card */}
            <div className="lg:col-span-2">
              <Card className="h-full bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
                <CardContent className="p-10">
                  <div className="flex flex-col justify-center gap-4">
                    <div className="flex -space-x-2">
                      <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold">
                        A
                      </div>
                      <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white font-semibold">
                        B
                      </div>
                      <div className="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center text-white font-semibold">
                        C
                      </div>
                      <div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center text-white font-semibold">
                        +5
                      </div>
                    </div>
                    <h2 className="text-xl font-semibold text-mono">
                      Welcome to AI4SE Dashboard
                      <br />
                      Your AI-Powered Software Engineering Hub
                    </h2>
                    <p className="text-sm font-normal text-secondary-foreground leading-5.5">
                      Enhance your development workflow with AI-powered tools
                      <br />
                      and insights. Join our community for cutting-edge
                      <br />
                      software engineering solutions.
                    </p>
                  </div>
                </CardContent>
                <div className="kt-card-footer justify-center">
                  <Button>
                    Get Started
                  </Button>
                </div>
              </Card>
            </div>
          </div>

          {/* Secondary Grid */}
          <div className="grid lg:grid-cols-3 gap-5 lg:gap-7.5 items-stretch">
            {/* Highlights Card */}
            <div className="lg:col-span-1">
              <Card className="h-full">
                <CardHeader>
                  <CardTitle>Recent Activity</CardTitle>
                </CardHeader>
                <CardContent className="flex flex-col gap-4 p-5 lg:p-7.5 lg:pt-4">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-sm font-normal text-foreground">Project deployed</span>
                      </div>
                      <span className="text-xs text-muted-foreground">2m ago</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-sm font-normal text-foreground">New team member</span>
                      </div>
                      <span className="text-xs text-muted-foreground">1h ago</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                        <span className="text-sm font-normal text-foreground">Code review pending</span>
                      </div>
                      <span className="text-xs text-muted-foreground">3h ago</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Chart Placeholder */}
            <div className="lg:col-span-2">
              <Card className="h-full">
                <CardHeader>
                  <CardTitle>Performance Overview</CardTitle>
                </CardHeader>
                <CardContent className="flex flex-col justify-center items-center h-64">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-muted rounded-lg flex items-center justify-center mb-4">
                      📈
                    </div>
                    <p className="text-muted-foreground">Chart visualization will be implemented here</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
