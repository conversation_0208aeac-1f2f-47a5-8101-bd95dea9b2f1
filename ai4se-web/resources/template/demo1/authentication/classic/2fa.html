<!--
Product: Metronic is a toolkit of UI components built with Tailwind CSS for developing scalable web applications quickly and efficiently
Version: v9.2.1
Author: Keenthemes
Contact: <EMAIL>
Website: https://www.keenthemes.com
Support: https://devs.keenthemes.com
Follow: https://www.twitter.com/keenthemes
License: https://keenthemes.com/metronic/tailwind/docs/getting-started/license
-->
<!DOCTYPE html>
<html class="h-full" data-kt-theme="true" data-kt-theme-mode="light" dir="ltr" lang="en">
 <head><base href="../../../../">
  <title>
   Metronic - Tailwind CSS 2FA
  </title>
  <meta charset="utf-8"/>
  <meta content="follow, index" name="robots"/>
  <link href="https://127.0.0.1:8001/metronic-tailwind-html/demo1/authentication/classic/2fa" rel="canonical"/>
  <meta content="width=device-width, initial-scale=1, shrink-to-fit=no" name="viewport"/>
  <meta content="Two-factor authentication page, using Tailwind CSS" name="description"/>
  <meta content="@keenthemes" name="twitter:site"/>
  <meta content="@keenthemes" name="twitter:creator"/>
  <meta content="summary_large_image" name="twitter:card"/>
  <meta content="Metronic - Tailwind CSS 2FA" name="twitter:title"/>
  <meta content="Two-factor authentication page, using Tailwind CSS" name="twitter:description"/>
  <meta content="assets/media/app/og-image.png" name="twitter:image"/>
  <meta content="https://127.0.0.1:8001/metronic-tailwind-html/demo1/authentication/classic/2fa" property="og:url"/>
  <meta content="en_US" property="og:locale"/>
  <meta content="website" property="og:type"/>
  <meta content="@keenthemes" property="og:site_name"/>
  <meta content="Metronic - Tailwind CSS 2FA" property="og:title"/>
  <meta content="Two-factor authentication page, using Tailwind CSS" property="og:description"/>
  <meta content="assets/media/app/og-image.png" property="og:image"/>
  <link href="assets/media/app/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="assets/media/app/favicon-32x32.png" rel="icon" sizes="32x32" type="image/png"/>
  <link href="assets/media/app/favicon-16x16.png" rel="icon" sizes="16x16" type="image/png"/>
  <link href="assets/media/app/favicon.ico" rel="shortcut icon"/>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet"/>
  <link href="assets/vendors/apexcharts/apexcharts.css" rel="stylesheet"/>
  <link href="assets/vendors/keenicons/styles.bundle.css" rel="stylesheet"/>
  <link href="assets/css/styles.css" rel="stylesheet"/>
 </head>
 <body class="antialiased flex h-full text-base text-foreground bg-background">
  <!-- Theme Mode -->
  <script>
   const defaultThemeMode = 'light'; // light|dark|system
			let themeMode;

			if (document.documentElement) {
				if (localStorage.getItem('kt-theme')) {
					themeMode = localStorage.getItem('kt-theme');
				} else if (
					document.documentElement.hasAttribute('data-kt-theme-mode')
				) {
					themeMode =
						document.documentElement.getAttribute('data-kt-theme-mode');
				} else {
					themeMode = defaultThemeMode;
				}

				if (themeMode === 'system') {
					themeMode = window.matchMedia('(prefers-color-scheme: dark)').matches
						? 'dark'
						: 'light';
				}

				document.documentElement.classList.add(themeMode);
			}
  </script>
  <!-- End of Theme Mode -->
  <!-- Page -->
  <style>
   .page-bg {
			background-image: url('assets/media/images/2600x1200/bg-10.png');
		}
		.dark .page-bg {
			background-image: url('assets/media/images/2600x1200/bg-10-dark.png');
		}
  </style>
  <div class="flex items-center justify-center grow bg-center bg-no-repeat page-bg">
   <div class="kt-card max-w-[380px] w-full" id="2fa_form">
    <form action="#" class="kt-card-content flex flex-col gap-5 p-10" method="post">
     <img alt="image" class="dark:hidden h-20 mb-2" src="assets/media/illustrations/34.svg"/>
     <img alt="image" class="light:hidden h-20 mb-2" src="assets/media/illustrations/34-dark.svg"/>
     <div class="text-center mb-2">
      <h3 class="text-lg font-medium text-mono mb-5">
       Verify your phone
      </h3>
      <div class="flex flex-col">
       <span class="text-sm text-secondary-foreground mb-1.5">
        Enter the verification code we sent to
       </span>
       <a class="text-sm font-medium text-mono" href="#">
        ****** 7859
       </a>
      </div>
     </div>
     <div class="flex flex-wrap justify-center gap-2.5">
      <input class="kt-input focus:border-primary/10 focus:ring-3 focus:ring-primary/10 size-10 shrink-0 px-0 text-center" maxlength="1" name="code_0" placeholder="" type="text" value=""/>
      <input class="kt-input focus:border-primary/10 focus:ring-3 focus:ring-primary/10 size-10 shrink-0 px-0 text-center" maxlength="1" name="code_1" placeholder="" type="text" value=""/>
      <input class="kt-input focus:border-primary/10 focus:ring-3 focus:ring-primary/10 size-10 shrink-0 px-0 text-center" maxlength="1" name="code_2" placeholder="" type="text" value=""/>
      <input class="kt-input focus:border-primary/10 focus:ring-3 focus:ring-primary/10 size-10 shrink-0 px-0 text-center" maxlength="1" name="code_3" placeholder="" type="text" value=""/>
      <input class="kt-input focus:border-primary/10 focus:ring-3 focus:ring-primary/10 size-10 shrink-0 px-0 text-center" maxlength="1" name="code_4" placeholder="" type="text" value=""/>
      <input class="kt-input focus:border-primary/10 focus:ring-3 focus:ring-primary/10 size-10 shrink-0 px-0 text-center" maxlength="1" name="code_5" placeholder="" type="text" value=""/>
     </div>
     <div class="flex items-center justify-center mb-2">
      <span class="text-2sm text-secondary-foreground me-1.5">
       Didn’t receive a code? (37s)
      </span>
      <a class="text-2sm kt-link" href="html/demo1/authentication/classic/sign-in.html">
       Resend
      </a>
     </div>
     <button class="kt-btn kt-btn-primary flex justify-center grow">
      Continue
     </button>
    </form>
   </div>
  </div>
  <!-- End of Page -->
  <!-- Scripts -->
  <script src="assets/js/core.bundle.js">
  </script>
  <script src="assets/vendors/ktui/ktui.min.js">
  </script>
  <script src="assets/vendors/apexcharts/apexcharts.min.js">
  </script>
  <!-- End of Scripts -->
 </body>
</html>
